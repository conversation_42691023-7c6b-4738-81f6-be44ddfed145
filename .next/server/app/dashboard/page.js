/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Downloads/Web Magang/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/dashboard/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFrRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2VyaW5oci9Eb3dubG9hZHMvV2ViIE1hZ2FuZy9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e8bfb0bc4d14\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvZXJpbmhyL0Rvd25sb2Fkcy9XZWIgTWFnYW5nL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlOGJmYjBiYzRkMTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'SMAP - Sistem Manajemen Anti Penyuapan',\n    description: 'Internal Corporate Web Application for Anti-Corruption Management System',\n    keywords: [\n        'SMAP',\n        'Anti-Corruption',\n        'Corporate',\n        'Telkom',\n        'Document Management'\n    ],\n    authors: [\n        {\n            name: 'Telkom Indonesia'\n        }\n    ],\n    viewport: 'width=device-width, initial-scale=1'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} font-sans h-full bg-gray-50`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"root\",\n                className: \"min-h-full\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYnVpbHRpbiUyRmdsb2JhbC1lcnJvci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmVyaW5ociUyRkRvd25sb2FkcyUyRldlYiUyME1hZ2FuZyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZlcmluaHIlMkZEb3dubG9hZHMlMkZXZWIlMjBNYWdhbmclMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmVyaW5ociUyRkRvd25sb2FkcyUyRldlYiUyME1hZ2FuZyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGYXN5bmMtbWV0YWRhdGEuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZlcmluaHIlMkZEb3dubG9hZHMlMkZXZWIlMjBNYWdhbmclMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRm1ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmVyaW5ociUyRkRvd25sb2FkcyUyRldlYiUyME1hZ2FuZyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGbGliJTJGbWV0YWRhdGElMkZnZW5lcmF0ZSUyRmljb24tbWFyay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmVyaW5ociUyRkRvd25sb2FkcyUyRldlYiUyME1hZ2FuZyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGbmV4dC1kZXZ0b29scyUyRnVzZXJzcGFjZSUyRmFwcCUyRnNlZ21lbnQtZXhwbG9yZXItbm9kZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc1BBQXdJO0FBQ3hJO0FBQ0Esb09BQStIO0FBQy9IO0FBQ0EsME9BQWtJO0FBQ2xJO0FBQ0Esb1JBQXVKO0FBQ3ZKO0FBQ0Esd09BQWlJO0FBQ2pJO0FBQ0EsNFBBQTJJO0FBQzNJO0FBQ0Esa1FBQThJO0FBQzlJO0FBQ0Esc1FBQWdKO0FBQ2hKO0FBQ0Esa09BQWlJO0FBQ2pJO0FBQ0EsNFFBQW1KIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZXJpbmhyL0Rvd25sb2Fkcy9XZWIgTWFnYW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYnVpbHRpbi9nbG9iYWwtZXJyb3IuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9lcmluaHIvRG93bmxvYWRzL1dlYiBNYWdhbmcvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2VyaW5oci9Eb3dubG9hZHMvV2ViIE1hZ2FuZy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZXJpbmhyL0Rvd25sb2Fkcy9XZWIgTWFnYW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9lcmluaHIvRG93bmxvYWRzL1dlYiBNYWdhbmcvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZXJpbmhyL0Rvd25sb2Fkcy9XZWIgTWFnYW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9lcmluaHIvRG93bmxvYWRzL1dlYiBNYWdhbmcvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9tZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2VyaW5oci9Eb3dubG9hZHMvV2ViIE1hZ2FuZy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9lcmluaHIvRG93bmxvYWRzL1dlYiBNYWdhbmcvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2VuZXJhdGUvaWNvbi1tYXJrLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZXJpbmhyL0Rvd25sb2Fkcy9XZWIgTWFnYW5nL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbmV4dC1kZXZ0b29scy91c2Vyc3BhY2UvYXBwL3NlZ21lbnQtZXhwbG9yZXItbm9kZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZXJpbmhyJTJGRG93bmxvYWRzJTJGV2ViJTIwTWFnYW5nJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFrRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2VyaW5oci9Eb3dubG9hZHMvV2ViIE1hZ2FuZy9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _store_documents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/documents */ \"(ssr)/./src/store/documents.ts\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(ssr)/./src/components/layout/AppLayout.tsx\");\n/* harmony import */ var _components_dashboard_SMAPInfo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/SMAPInfo */ \"(ssr)/./src/components/dashboard/SMAPInfo.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,DocumentTextIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardPage() {\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const { getAccessibleDocuments, getDocumentProgress } = (0,_store_documents__WEBPACK_IMPORTED_MODULE_2__.useDocuments)();\n    if (!user) return null;\n    const accessibleDocuments = getAccessibleDocuments(user.role);\n    const completedDocs = accessibleDocuments.filter((doc)=>getDocumentProgress(doc.id, user.id) === 100).length;\n    const totalDocs = accessibleDocuments.length;\n    const overallProgress = totalDocs > 0 ? Math.round(completedDocs / totalDocs * 100) : 0;\n    const handleDocumentClick = (documentId, openInNewTab)=>{\n        window.open(`/dokumen/${documentId}`, openInNewTab ? '_blank' : '_self');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"gradient-primary rounded-2xl p-8 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"font-gotham-rounded text-3xl font-bold mb-2 text-white\",\n                                        children: [\n                                            \"Selamat Datang, \",\n                                            user.name,\n                                            \"!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-white mb-1\",\n                                        children: [\n                                            \"Role: \",\n                                            (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-white/80\",\n                                        children: \"Kelola dokumen SMAP dengan mudah\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this),\n                            (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-gotham-rounded text-3xl font-bold mb-1\",\n                                        children: [\n                                            overallProgress,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-gotham text-white/90\",\n                                        children: \"Progress Keseluruhan\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-50 rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-2xl font-bold text-gray-900\",\n                                                children: totalDocs\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-secondary\",\n                                                children: \"Total Dokumen\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-50 rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-gotham-rounded text-2xl font-bold text-gray-900\",\n                                                children: completedDocs\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-gotham text-secondary\",\n                                                children: \"Selesai\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SMAPInfo__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pb-6 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-gotham-rounded text-2xl font-bold text-gray-900\",\n                                    children: \"Dokumen SMAP\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-gotham text-secondary mt-1\",\n                                    children: \"Kelola dan akses dokumen anti penyuapan\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-6 md:hidden\",\n                                    children: accessibleDocuments.map((doc)=>{\n                                        const progress = getDocumentProgress(doc.id, user.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200\",\n                                            onClick: ()=>handleDocumentClick(doc.id, doc.openInNewTab),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 bg-red-100 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-red-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 115,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 114,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: doc.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 118,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    progress,\n                                                                                    \"% selesai\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 121,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 border border-gray-300 rounded-lg hover:border-red-500 hover:bg-red-50 transition-all duration-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-600 hover:text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-red-600 h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: `${progress}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, doc.id, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2 space-y-6\",\n                                                children: accessibleDocuments.filter((_, index)=>index % 2 === 0).map((doc)=>{\n                                                    const progress = getDocumentProgress(doc.id, user.id);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200\",\n                                                        onClick: ()=>handleDocumentClick(doc.id, doc.openInNewTab),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"p-2 bg-red-100 rounded-lg\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                        lineNumber: 165,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 164,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"font-semibold text-gray-900\",\n                                                                                            children: doc.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                            lineNumber: 168,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600\",\n                                                                                            children: [\n                                                                                                progress,\n                                                                                                \"% selesai\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                            lineNumber: 171,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 167,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 163,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 border border-gray-300 rounded-lg hover:border-red-500 hover:bg-red-50 transition-all duration-200\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-600 hover:text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 176,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 175,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-red-600 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: `${progress}%`\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, doc.id, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2 space-y-6\",\n                                                children: accessibleDocuments.filter((_, index)=>index % 2 === 1).map((doc)=>{\n                                                    const progress = getDocumentProgress(doc.id, user.id);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors duration-200\",\n                                                        onClick: ()=>handleDocumentClick(doc.id, doc.openInNewTab),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"p-2 bg-red-100 rounded-lg\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                        lineNumber: 212,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 211,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"font-semibold text-gray-900\",\n                                                                                            children: doc.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                            lineNumber: 215,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-600\",\n                                                                                            children: [\n                                                                                                progress,\n                                                                                                \"% selesai\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                            lineNumber: 218,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                    lineNumber: 214,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 border border-gray-300 rounded-lg hover:border-red-500 hover:bg-red-50 transition-all duration-200\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_DocumentTextIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-600 hover:text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 222,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                (user.role === 'admin_super' || user.role === 'admin_biasa' || user.role === 'scoopers') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-red-600 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: `${progress}%`\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, doc.id, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n            lineNumber: 31,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/Web Magang/src/app/dashboard/page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/SMAPInfo.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/SMAPInfo.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SMAPInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,TrophyIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SMAPInfo() {\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-card p-4 hover:shadow-card-hover transition-all duration-200 border border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center p-2 bg-green-50 rounded-lg mb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-5 w-5 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-gotham-rounded text-base font-bold text-gray-900 mb-2\",\n                            children: \"Tahun Implementasi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-gotham-rounded text-2xl font-bold text-gray-900 mb-1\",\n                            children: currentYear\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-gotham text-gray-600 text-sm\",\n                            children: \"Tahun Berjalan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-card p-4 hover:shadow-card-hover transition-all duration-200 border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center p-2 bg-purple-50 rounded-lg mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_TrophyIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-5 w-5 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-gotham-rounded text-base font-bold text-gray-900\",\n                                children: \"Tujuan SMAP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-gotham text-gray-600 text-sm leading-relaxed mb-2\",\n                                children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-gotham text-gray-600 text-sm leading-relaxed\",\n                                children: \"Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/dashboard/SMAPInfo.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/SMAPInfo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _store_notifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/notifications */ \"(ssr)/./src/store/notifications.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_NotificationDropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/NotificationDropdown */ \"(ssr)/./src/components/ui/NotificationDropdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction AppLayout({ children }) {\n    const { user, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const { unreadCount, initializeNotifications } = (0,_store_notifications__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Function to determine if a menu item is active\n    const isActive = (path)=>{\n        if (path === '/dashboard') {\n            return pathname === '/dashboard' || pathname === '/';\n        }\n        if (path === '/repository') {\n            return pathname.startsWith('/repository');\n        }\n        if (path === '/config') {\n            return pathname === '/config';\n        }\n        return pathname === path;\n    };\n    // Function to get menu item classes\n    const getMenuClasses = (path, isMobile = false)=>{\n        const baseClasses = \"font-gotham-rounded font-medium transition-colors duration-200\";\n        const activeClasses = \"text-red-600\"; // Active state - red color\n        const inactiveClasses = \"text-secondary hover:text-primary\";\n        const mobileClasses = isMobile ? \"hover:bg-gray-50 block px-3 py-2 rounded-md text-base\" : \"\";\n        const isActiveItem = isActive(path);\n        const colorClasses = isActiveItem ? activeClasses : inactiveClasses;\n        return `${baseClasses} ${colorClasses} ${mobileClasses}`.trim();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppLayout.useEffect\": ()=>{\n            if (user) {\n                initializeNotifications(user.id, user.role);\n            }\n        }\n    }[\"AppLayout.useEffect\"], [\n        user,\n        initializeNotifications\n    ]);\n    // Initialize document structure when active program changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppLayout.useEffect\": ()=>{\n            if (activeProgram) {\n                initializeDocumentStructure(activeProgram.id);\n            }\n        }\n    }[\"AppLayout.useEffect\"], [\n        activeProgram,\n        initializeDocumentStructure\n    ]);\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const closeMobileMenu = ()=>{\n        setIsMobileMenuOpen(false);\n    };\n    if (!user) {\n        // Redirect to login instead of showing loading\n        if (false) {}\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Mengarahkan ke halaman login...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"/smap-logo.png\",\n                                                alt: \"SMAP Logo\",\n                                                width: 120,\n                                                height: 48,\n                                                className: \"h-12 w-auto transition-transform duration-200 hover:scale-105\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/dashboard\",\n                                                    className: getMenuClasses('/dashboard'),\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/config\",\n                                                            className: getMenuClasses('/config'),\n                                                            children: \"Config\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/manage-group\",\n                                                            className: getMenuClasses('/manage-group'),\n                                                            children: \"Manage Group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/manage-program\",\n                                                    className: getMenuClasses('/manage-program'),\n                                                    children: \"Manage Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/repository\",\n                                                    className: getMenuClasses('/repository'),\n                                                    children: \"Risk Assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_NotificationDropdown__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-gotham text-sm text-secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs ml-1\",\n                                                            children: [\n                                                                \"(\",\n                                                                (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        logout();\n                                                        window.location.href = '/login';\n                                                    },\n                                                    className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMobileMenu,\n                                                className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary\",\n                                                \"aria-expanded\": \"false\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open main menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"block h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    onClick: closeMobileMenu,\n                                    className: getMenuClasses('/dashboard', true),\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                (user.role === 'admin_super' || user.role === 'admin_biasa') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/config\",\n                                            onClick: closeMobileMenu,\n                                            className: getMenuClasses('/config', true),\n                                            children: \"Config\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/manage-group\",\n                                            onClick: closeMobileMenu,\n                                            className: getMenuClasses('/manage-group', true),\n                                            children: \"Manage Group\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                user.role === 'admin_super' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/manage-program\",\n                                    onClick: closeMobileMenu,\n                                    className: getMenuClasses('/manage-program', true),\n                                    children: \"Manage Program\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/repository\",\n                                    onClick: closeMobileMenu,\n                                    className: getMenuClasses('/repository', true),\n                                    children: \"Risk Assessment\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-4 pb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-base font-medium text-gray-800\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"(\",\n                                                        (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.getRoleDisplayName)(user.role),\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    logout();\n                                                    window.location.href = '/login';\n                                                },\n                                                className: \"font-gotham-rounded font-medium text-sm text-primary hover:text-primary-700 transition-colors duration-200 w-full text-left\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/layout/AppLayout.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/NotificationDropdown.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/NotificationDropdown.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationDropdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_notifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/notifications */ \"(ssr)/./src/store/notifications.ts\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/esm/locale/id/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction NotificationDropdown({ className = '' }) {\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { getRecentNotifications, markAsRead, markAllAsRead, unreadCount } = (0,_store_notifications__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const recentNotifications = user ? getRecentNotifications(user.id, 5) : [];\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationDropdown.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"NotificationDropdown.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationDropdown.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"NotificationDropdown.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"NotificationDropdown.useEffect\"];\n        }\n    }[\"NotificationDropdown.useEffect\"], []);\n    const handleNotificationClick = (notificationId, actionUrl)=>{\n        markAsRead(notificationId);\n        if (actionUrl) {\n            window.location.href = actionUrl;\n        }\n    };\n    const handleMarkAllAsRead = ()=>{\n        if (user) {\n            markAllAsRead(user.id);\n        }\n    };\n    const formatDate = (date)=>{\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(date, 'dd MMM yyyy, HH:mm', {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n            });\n        } catch  {\n            return 'Invalid date';\n        }\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'document_update':\n                return '📄';\n            case 'success':\n                return '✅';\n            case 'warning':\n                return '⚠️';\n            case 'error':\n                return '❌';\n            default:\n                return '💡';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg transition-colors duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: unreadCount > 9 ? '9+' : unreadCount\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Notifikasi\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"p-1 hover:bg-gray-100 rounded-lg transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleMarkAllAsRead,\n                                className: \"text-sm text-primary hover:text-primary-700 mt-1\",\n                                children: \"Tandai semua sudah dibaca\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: recentNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-8 w-8 mx-auto mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Tidak ada notifikasi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-100\",\n                            children: recentNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>handleNotificationClick(notification.id, notification.actionUrl),\n                                    className: `p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200 ${!notification.isRead ? 'bg-blue-50' : ''}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 text-lg\",\n                                                children: getNotificationIcon(notification.type)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-sm ${!notification.isRead ? 'font-semibold' : 'font-medium'} text-gray-900`,\n                                                        children: notification.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mt-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: formatDate(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            notification.documentType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\",\n                                                                children: notification.documentType\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    notification.updatedBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"oleh \",\n                                                            notification.updatedBy\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 23\n                                            }, this),\n                                            !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    recentNotifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/notifikasi\",\n                            className: \"block text-center text-sm text-primary hover:text-primary-700 font-medium\",\n                            children: \"Lihat Semua Notifikasi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/Web Magang/src/components/ui/NotificationDropdown.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/NotificationDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleColor: () => (/* binding */ getRoleColor),\n/* harmony export */   getRoleDisplayName: () => (/* binding */ getRoleDisplayName),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\n// Default users data with username (angka) and password - no email\nconst DEFAULT_USERS = {\n    '1001': {\n        id: '1',\n        name: '1001',\n        email: '',\n        role: 'admin_super',\n        lastLogin: new Date(),\n        password: '1001'\n    },\n    '1002': {\n        id: '2',\n        name: '1002',\n        email: '',\n        role: 'admin_biasa',\n        lastLogin: new Date(),\n        password: '1002'\n    },\n    '1003': {\n        id: '3',\n        name: '1003',\n        email: '',\n        role: 'reviewer',\n        lastLogin: new Date(),\n        password: '1003'\n    },\n    '1004': {\n        id: '4',\n        name: '1004',\n        email: '',\n        role: 'scoopers',\n        lastLogin: new Date(),\n        password: '1004'\n    },\n    '1005': {\n        id: '5',\n        name: '1005',\n        email: '',\n        role: 'karyawan',\n        lastLogin: new Date(),\n        password: '1005'\n    }\n};\n// Function to get users from localStorage or default\nconst getUsers = ()=>{\n    if (false) {}\n    return DEFAULT_USERS;\n};\n// Initialize user from localStorage if available\nconst getInitialUser = ()=>{\n    if (true) return null;\n    try {\n        const savedUser = localStorage.getItem('smap_user');\n        if (savedUser) {\n            const user = JSON.parse(savedUser);\n            // Migration: Update 'scopers' to 'scoopers'\n            if (user.role === 'scopers') {\n                user.role = 'scoopers';\n                // Save updated user back to localStorage\n                localStorage.setItem('smap_user', JSON.stringify(user));\n            }\n            return user;\n        }\n        return null;\n    } catch  {\n        return null;\n    }\n};\nconst useAuth = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        user: getInitialUser(),\n        isAuthenticated: !!getInitialUser(),\n        isLoading: false,\n        login: async (username, password)=>{\n            set({\n                isLoading: true\n            });\n            // Get current users from localStorage or default\n            const users = getUsers();\n            const user = users[username];\n            // Simple validation - username dan password harus sama\n            if (user && password === username) {\n                const userWithLastLogin = {\n                    ...user,\n                    lastLogin: new Date()\n                };\n                // Save to localStorage\n                if (false) {}\n                set({\n                    user: userWithLastLogin,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n                return true;\n            }\n            set({\n                isLoading: false\n            });\n            return false;\n        },\n        logout: ()=>{\n            // Remove only user session from localStorage, keep user-specific data\n            if (false) {}\n            set({\n                user: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        hasRole: (roles)=>{\n            const { user } = get();\n            return user ? roles.includes(user.role) : false;\n        }\n    }));\n// Helper functions\nconst getRoleDisplayName = (role)=>{\n    const roleNames = {\n        admin_super: 'Admin Super',\n        admin_biasa: 'Admin Biasa',\n        reviewer: 'Reviewer',\n        scoopers: 'Scoopers',\n        karyawan: 'Karyawan'\n    };\n    return roleNames[role];\n};\nconst getRoleColor = (role)=>{\n    const roleColors = {\n        admin_super: 'bg-red-100 text-red-800',\n        admin_biasa: 'bg-blue-100 text-blue-800',\n        reviewer: 'bg-green-100 text-green-800',\n        scoopers: 'bg-purple-100 text-purple-800',\n        karyawan: 'bg-gray-100 text-gray-800'\n    };\n    return roleColors[role];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/documents.ts":
/*!********************************!*\
  !*** ./src/store/documents.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocuments: () => (/* binding */ useDocuments)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\n// Mock documents data\nconst DOCUMENTS = [\n    {\n        id: 'doc-1',\n        title: 'Pedoman Peraturan Perusahaan',\n        description: 'Dokumen pedoman dan peraturan perusahaan terkait anti penyuapan',\n        progress: 0,\n        isCompleted: false,\n        isLocked: false,\n        accessibleRoles: [\n            'admin_super',\n            'admin_biasa',\n            'procurement',\n            'scoopers',\n            'karyawan',\n            'reviewer'\n        ],\n        order: 1\n    },\n    {\n        id: 'doc-2',\n        title: 'Prosedur dan Instruksi Kerja',\n        description: 'Prosedur operasional standar dan instruksi kerja',\n        progress: 0,\n        isCompleted: false,\n        isLocked: false,\n        accessibleRoles: [\n            'admin_super',\n            'admin_biasa',\n            'procurement',\n            'scoopers'\n        ],\n        order: 2\n    },\n    {\n        id: 'doc-3',\n        title: 'Evidence Mapping Klausul',\n        description: 'Pemetaan evidence untuk setiap klausul (khusus Scoopers)',\n        progress: 0,\n        isCompleted: false,\n        isLocked: false,\n        accessibleRoles: [\n            'admin_super',\n            'scoopers'\n        ],\n        order: 3\n    },\n    {\n        id: 'doc-4',\n        title: 'Fungsi Kepatuhan Anti Penyuapan',\n        description: 'Dokumen fungsi kepatuhan dan monitoring',\n        progress: 0,\n        isCompleted: false,\n        isLocked: false,\n        accessibleRoles: [\n            'admin_super',\n            'admin_biasa',\n            'procurement',\n            'scoopers'\n        ],\n        order: 4\n    }\n];\nconst useDocuments = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        documents: DOCUMENTS,\n        userProgress: {},\n        getAccessibleDocuments: (userRole)=>{\n            const { documents } = get();\n            return documents.filter((doc)=>doc.accessibleRoles.includes(userRole)).sort((a, b)=>a.order - b.order);\n        },\n        updateProgress: (documentId, userId, progress)=>{\n            set((state)=>({\n                    userProgress: {\n                        ...state.userProgress,\n                        [userId]: {\n                            ...state.userProgress[userId],\n                            [documentId]: Math.min(100, Math.max(0, progress))\n                        }\n                    }\n                }));\n        },\n        completeDocument: (documentId, userId)=>{\n            const { updateProgress } = get();\n            updateProgress(documentId, userId, 100);\n            // Unlock next document\n            set((state)=>{\n                const documents = [\n                    ...state.documents\n                ];\n                const currentDoc = documents.find((d)=>d.id === documentId);\n                if (currentDoc) {\n                    const nextDoc = documents.find((d)=>d.order === currentDoc.order + 1);\n                    if (nextDoc) {\n                        nextDoc.isLocked = false;\n                    }\n                }\n                return {\n                    documents\n                };\n            });\n        },\n        getDocumentProgress: (documentId, userId)=>{\n            const { userProgress } = get();\n            return userProgress[userId]?.[documentId] || 0;\n        },\n        isDocumentUnlocked: (documentId, userId, userRole)=>{\n            const { documents, getDocumentProgress } = get();\n            const doc = documents.find((d)=>d.id === documentId);\n            if (!doc) return false;\n            if (!doc.accessibleRoles.includes(userRole)) return false;\n            if (!doc.isLocked) return true;\n            // Check if previous document is completed\n            const prevDoc = documents.find((d)=>d.order === doc.order - 1);\n            if (!prevDoc) return true;\n            return getDocumentProgress(prevDoc.id, userId) === 100;\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/documents.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/notifications.ts":
/*!************************************!*\
  !*** ./src/store/notifications.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   notificationConfig: () => (/* binding */ notificationConfig),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n\n// Mock notifications generator with document history\nconst generateNotifications = (userId, userRole)=>{\n    const documentHistoryNotifications = [\n        {\n            title: 'Dokumen Diperbarui',\n            message: 'Dokumen \"Pedoman Peraturan Perusahaan\" telah diperbarui.',\n            type: 'document_update',\n            isRead: false,\n            actionUrl: '/dokumen',\n            documentName: 'Pedoman Peraturan Perusahaan',\n            documentType: 'Manual',\n            updatedBy: 'Ahmad Rizki',\n            updateAction: 'updated'\n        },\n        {\n            title: 'Dokumen Baru Dibuat',\n            message: 'Dokumen \"Prosedur Anti Korupsi\" telah dibuat.',\n            type: 'document_update',\n            isRead: false,\n            actionUrl: '/dokumen',\n            documentName: 'Prosedur Anti Korupsi',\n            documentType: 'Prosedur',\n            updatedBy: 'Siti Nurhaliza',\n            updateAction: 'created'\n        },\n        {\n            title: 'Evidence Diupload',\n            message: 'Evidence \"Laporan Audit Internal\" telah diupload.',\n            type: 'document_update',\n            isRead: true,\n            actionUrl: '/dokumen',\n            documentName: 'Laporan Audit Internal',\n            documentType: 'Evidence',\n            updatedBy: 'Budi Santoso',\n            updateAction: 'created'\n        },\n        {\n            title: 'Dokumen Direview',\n            message: 'Dokumen \"Policy Anti Penyuapan\" telah direview.',\n            type: 'document_update',\n            isRead: true,\n            actionUrl: '/dokumen',\n            documentName: 'Policy Anti Penyuapan',\n            documentType: 'Policy',\n            updatedBy: 'Dr. Indira Sari',\n            updateAction: 'reviewed'\n        },\n        {\n            title: 'Form Template Diperbarui',\n            message: 'Template \"Form Pelaporan Gratifikasi\" telah diperbarui.',\n            type: 'document_update',\n            isRead: false,\n            actionUrl: '/dokumen',\n            documentName: 'Form Pelaporan Gratifikasi',\n            documentType: 'Form',\n            updatedBy: 'Eko Prasetyo',\n            updateAction: 'updated'\n        }\n    ];\n    const systemNotifications = [\n        {\n            title: 'Sistem Maintenance',\n            message: 'Sistem akan menjalani maintenance pada tanggal 30 Januari 2024 pukul 02:00 WIB.',\n            type: 'warning',\n            isRead: true,\n            actionUrl: null\n        }\n    ];\n    const roleSpecificNotifications = {\n        admin_super: [\n            {\n                title: 'Laporan Bulanan Siap',\n                message: 'Laporan bulanan SMAP untuk bulan Januari 2024 telah siap untuk direview.',\n                type: 'success',\n                isRead: false,\n                actionUrl: '/reports'\n            }\n        ],\n        admin_biasa: [\n            {\n                title: 'Dokumen Perlu Review',\n                message: 'Terdapat 2 dokumen yang memerlukan review dari administrator.',\n                type: 'warning',\n                isRead: false,\n                actionUrl: '/dokumen'\n            }\n        ],\n        procurement: [],\n        scoopers: [\n            {\n                title: 'Evidence Mapping Tersedia',\n                message: 'Dokumen Evidence Mapping Klausul telah tersedia untuk unit audit Anda.',\n                type: 'document_update',\n                isRead: false,\n                actionUrl: '/dokumen'\n            }\n        ],\n        karyawan: [\n            {\n                title: 'Training Anti Korupsi',\n                message: 'Training wajib anti korupsi akan diadakan pada tanggal 15 Februari 2024.',\n                type: 'info',\n                isRead: false,\n                actionUrl: '/training'\n            }\n        ]\n    };\n    const allNotifications = [\n        ...documentHistoryNotifications,\n        ...systemNotifications,\n        ...roleSpecificNotifications[userRole] || []\n    ];\n    return allNotifications.map((notification, index)=>({\n            ...notification,\n            id: `notif-${userId}-${index + 1}`,\n            userId,\n            createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)\n        })).sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()); // Sort by newest first\n};\nconst useNotifications = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        notifications: [],\n        unreadCount: 0,\n        initializeNotifications: (userId, userRole)=>{\n            const notifications = generateNotifications(userId, userRole);\n            const unreadCount = notifications.filter((n)=>!n.isRead).length;\n            set({\n                notifications,\n                unreadCount\n            });\n        },\n        markAsRead: (notificationId)=>{\n            set((state)=>{\n                const updatedNotifications = state.notifications.map((notification)=>notification.id === notificationId ? {\n                        ...notification,\n                        isRead: true\n                    } : notification);\n                const unreadCount = updatedNotifications.filter((n)=>!n.isRead).length;\n                return {\n                    notifications: updatedNotifications,\n                    unreadCount\n                };\n            });\n        },\n        markAllAsRead: (userId)=>{\n            set((state)=>{\n                const updatedNotifications = state.notifications.map((notification)=>notification.userId === userId ? {\n                        ...notification,\n                        isRead: true\n                    } : notification);\n                return {\n                    notifications: updatedNotifications,\n                    unreadCount: 0\n                };\n            });\n        },\n        addNotification: (notification)=>{\n            const newNotification = {\n                ...notification,\n                id: `notif-${Date.now()}`,\n                createdAt: new Date()\n            };\n            set((state)=>({\n                    notifications: [\n                        newNotification,\n                        ...state.notifications\n                    ],\n                    unreadCount: state.unreadCount + 1\n                }));\n        },\n        getUserNotifications: (userId)=>{\n            const { notifications } = get();\n            return notifications.filter((n)=>n.userId === userId);\n        },\n        getFilteredNotifications: (userId, filter)=>{\n            const { notifications } = get();\n            let filtered = notifications.filter((n)=>n.userId === userId);\n            if (filter) {\n                if (filter.documentType) {\n                    filtered = filtered.filter((n)=>n.documentType === filter.documentType);\n                }\n                if (filter.dateFrom) {\n                    filtered = filtered.filter((n)=>n.createdAt >= filter.dateFrom);\n                }\n                if (filter.dateTo) {\n                    filtered = filtered.filter((n)=>n.createdAt <= filter.dateTo);\n                }\n                if (filter.updatedBy) {\n                    filtered = filtered.filter((n)=>n.updatedBy?.toLowerCase().includes(filter.updatedBy.toLowerCase()));\n                }\n                if (filter.isRead !== undefined) {\n                    filtered = filtered.filter((n)=>n.isRead === filter.isRead);\n                }\n            }\n            return filtered.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        },\n        getRecentNotifications: (userId, limit = 5)=>{\n            const { notifications } = get();\n            return notifications.filter((n)=>n.userId === userId).sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);\n        }\n    }));\n// Notification type configurations\nconst notificationConfig = {\n    info: {\n        icon: '💡',\n        color: 'bg-blue-50 border-blue-200 text-blue-900'\n    },\n    warning: {\n        icon: '⚠️',\n        color: 'bg-yellow-50 border-yellow-200 text-yellow-900'\n    },\n    success: {\n        icon: '✅',\n        color: 'bg-green-50 border-green-200 text-green-900'\n    },\n    error: {\n        icon: '❌',\n        color: 'bg-red-50 border-red-200 text-red-900'\n    },\n    document_update: {\n        icon: '📄',\n        color: 'bg-red-50 border-red-200 text-red-900'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/notifications.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/use-sync-external-store","vendor-chunks/zustand","vendor-chunks/date-fns","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ferinhr%2FDownloads%2FWeb%20Magang&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();